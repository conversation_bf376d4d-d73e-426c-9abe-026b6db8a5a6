import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { HttpService } from '@nestjs/axios';
import { lastValueFrom } from 'rxjs';

@Injectable()
export class DnsService implements OnModuleInit {
  private readonly logger = new Logger(DnsService.name);
  private readonly baseDomain: string;
  private apiToken: string;

  constructor(
    private readonly configService: ConfigService,
    private readonly httpService: HttpService,
  ) {
    this.baseDomain = this.configService.get<string>('BASE_DOMAIN', 'geerd.net');
  }

  async onModuleInit() {
    this.apiToken = this.configService.get<string>('DIGITALOCEAN_API_TOKEN');
    if (!this.apiToken) {
      this.logger.error('DIGITALOCEAN_API_TOKEN is not set - DNS operations will fail');
    } else {
      this.logger.log(`DNS Service initialized with base domain: ${this.baseDomain}`);
    }
  }

  /**
   * Create a DNS record in DigitalOcean
   * @param serverId The server ID (will be used as subdomain)
   * @param ipAddress The server IP address
   * @returns True if successful, false otherwise
   */
  async createDnsRecord(serverId: string, ipAddress: string): Promise<boolean> {
    try {
      if (!this.validateInputs(serverId, ipAddress) || !this.validateApiToken()) {
        return false;
      }

      const sanitizedServerId = this.sanitizeServerId(serverId);
      this.logger.log(`Creating DNS record: ${sanitizedServerId}.${this.baseDomain} -> ${ipAddress}`);

      // Check if record already exists
      const existingRecord = await this.findExistingRecord(sanitizedServerId);
      if (existingRecord) {
        if (existingRecord.data === ipAddress) {
          this.logger.log(`DNS record already exists with correct IP: ${sanitizedServerId}.${this.baseDomain} -> ${ipAddress}`);
          return true;
        } else {
          this.logger.log(`Updating existing DNS record: ${sanitizedServerId}.${this.baseDomain} from ${existingRecord.data} to ${ipAddress}`);
          return await this.updateRecord(existingRecord.id, ipAddress, sanitizedServerId);
        }
      }

      // Create new record only if none exists
      return await this.createNewRecord(sanitizedServerId, ipAddress);
    } catch (error) {
      return this.handleCreateRecordError(error, serverId);
    }
  }

  /**
   * Delete a DNS record from DigitalOcean with retry logic
   * @param serverId The server ID (subdomain)
   * @returns True if successful, false otherwise
   */
  async deleteDnsRecord(serverId: string): Promise<boolean> {
    const maxRetries = 3;
    const retryDelay = 2000; // 2 seconds

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        if (!this.validateServerId(serverId) || !this.validateApiToken()) {
          return false;
        }

        const sanitizedServerId = this.sanitizeServerId(serverId);
        this.logger.log(`🌐 Deleting DNS record for: ${sanitizedServerId}.${this.baseDomain} (attempt ${attempt}/${maxRetries})`);

        const records = await this.getRecordsByName(sanitizedServerId);
        if (records.length === 0) {
          this.logger.log(`✅ No DNS records found for ${sanitizedServerId}.${this.baseDomain} (already deleted)`);
          return true;
        }

        const result = await this.deleteMatchingRecords(records, sanitizedServerId);
        if (result) {
          this.logger.log(`✅ Successfully deleted DNS record for ${sanitizedServerId}.${this.baseDomain}`);
          return true;
        }

        // If not the last attempt, wait before retrying
        if (attempt < maxRetries) {
          this.logger.warn(`⚠️ DNS deletion attempt ${attempt} failed, retrying in ${retryDelay}ms...`);
          await this.delay(retryDelay);
        }
      } catch (error) {
        this.logger.error(`❌ DNS deletion attempt ${attempt} failed: ${error.message}`);

        // If not the last attempt, wait before retrying
        if (attempt < maxRetries) {
          await this.delay(retryDelay);
        }
      }
    }

    this.logger.error(`❌ Failed to delete DNS record after ${maxRetries} attempts`);
    return false;
  }

  /**
   * Simple delay utility
   */
  private async delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Verify if a DNS record exists and points to the correct IP
   * @param serverId The server ID (subdomain)
   * @param ipAddress The expected IP address
   * @returns True if the record exists and points to the correct IP, false otherwise
   */
  async verifyDnsRecord(serverId: string, ipAddress: string): Promise<boolean> {
    try {
      if (!this.validateInputs(serverId, ipAddress) || !this.validateApiToken()) {
        return false;
      }

      const sanitizedServerId = this.sanitizeServerId(serverId);
      this.logger.log(`Verifying DNS record: ${sanitizedServerId}.${this.baseDomain} -> ${ipAddress}`);

      const records = await this.getRecordsByName(sanitizedServerId);
      if (records.length === 0) {
        this.logger.warn(`No DNS records found for ${sanitizedServerId}.${this.baseDomain}`);
        return false;
      }

      const matchingRecord = records.find(record => record.name === sanitizedServerId && record.type === 'A');
      if (!matchingRecord) {
        this.logger.warn(`No matching A record found for ${sanitizedServerId}.${this.baseDomain}`);
        return false;
      }

      const matches = matchingRecord.data === ipAddress;
      if (matches) {
        this.logger.log(`DNS record verified: ${sanitizedServerId}.${this.baseDomain} -> ${ipAddress}`);
      } else {
        this.logger.warn(`DNS record has incorrect IP: ${sanitizedServerId}.${this.baseDomain} -> ${matchingRecord.data} (expected ${ipAddress})`);
      }

      return matches;
    } catch (error) {
      this.logger.error(`Failed to verify DNS record: ${error.message}`);
      return false;
    }
  }

  /**
   * Utility Methods
   */

  private validateInputs(serverId: string, ipAddress: string): boolean {
    if (!serverId || !ipAddress) {
      this.logger.error('Invalid parameters: serverId and ipAddress are required');
      return false;
    }
    return true;
  }

  private validateServerId(serverId: string): boolean {
    if (!serverId) {
      this.logger.error('Invalid parameter: serverId is required');
      return false;
    }
    return true;
  }

  private validateApiToken(): boolean {
    if (!this.apiToken) {
      this.logger.error('DIGITALOCEAN_API_TOKEN is not set');
      return false;
    }
    return true;
  }

  private sanitizeServerId(serverId: string): string {
    // Remove any existing domain suffix to prevent double domains
    let sanitized = serverId.replace(/\.geerd\.net$/i, '');

    // Remove invalid DNS characters
    sanitized = sanitized.replace(/[^a-zA-Z0-9-]/g, '');

    if (sanitized !== serverId) {
      this.logger.warn(`ServerId sanitized from '${serverId}' to '${sanitized}'`);
    }

    // Validate length (DNS labels must be 1-63 characters)
    if (sanitized.length === 0 || sanitized.length > 63) {
      this.logger.error(`Invalid DNS label length: '${sanitized}' (must be 1-63 characters)`);
      throw new Error(`Invalid DNS label length: ${sanitized.length}`);
    }

    return sanitized;
  }

  private getApiHeaders(): { Authorization: string; 'Content-Type'?: string } {
    return {
      Authorization: `Bearer ${this.apiToken}`,
      'Content-Type': 'application/json',
    };
  }

  private async getRecordsByName(serverId: string): Promise<any[]> {
    const response = await lastValueFrom(
      this.httpService.get(
        `https://api.digitalocean.com/v2/domains/${this.baseDomain}/records?name=${serverId}`,
        { headers: this.getApiHeaders() }
      )
    );

    if (response.status !== 200 || !response.data?.domain_records) {
      throw new Error(`Failed to get DNS records: ${response.status}`);
    }

    return response.data.domain_records;
  }

  private async findExistingRecord(serverId: string): Promise<any | null> {
    try {
      const records = await this.getRecordsByName(serverId);
      // Find A records with matching name - return the first one only
      const aRecord = records.find(record => record.name === serverId && record.type === 'A');

      if (aRecord) {
        // If there are multiple A records, log a warning but use the first one
        const aRecords = records.filter(record => record.name === serverId && record.type === 'A');
        if (aRecords.length > 1) {
          this.logger.warn(`Multiple DNS A records found for ${serverId}, using first one. Total: ${aRecords.length}`);
        }
      }

      return aRecord || null;
    } catch (error) {
      this.logger.warn(`Failed to check existing DNS records: ${error.message}`);
      return null;
    }
  }

  private async updateRecord(recordId: string, ipAddress: string, serverId: string): Promise<boolean> {
    const response = await lastValueFrom(
      this.httpService.put(
        `https://api.digitalocean.com/v2/domains/${this.baseDomain}/records/${recordId}`,
        { data: ipAddress },
        { headers: this.getApiHeaders() }
      )
    );

    if (response.status === 200) {
      this.logger.log(`DNS record updated successfully: ${serverId}.${this.baseDomain} -> ${ipAddress}`);
      return true;
    }
    return false;
  }

  private async createNewRecord(serverId: string, ipAddress: string): Promise<boolean> {
    try {
      const response = await lastValueFrom(
        this.httpService.post(
          `https://api.digitalocean.com/v2/domains/${this.baseDomain}/records`,
          {
            type: 'A',
            name: serverId,
            data: ipAddress,
            ttl: 300
          },
          { headers: this.getApiHeaders() }
        )
      );

      if (response.status === 201) {
        this.logger.log(`DNS record created successfully: ${serverId}.${this.baseDomain} -> ${ipAddress}`);
        return true;
      } else {
        this.logger.warn(`Unexpected response when creating DNS record: ${response.status}`);
        return false;
      }
    } catch (error) {
      // If the record already exists, that's fine
      if (error.response?.status === 422 &&
          error.response?.data?.message?.includes('already exists')) {
        this.logger.log(`DNS record already exists: ${serverId}.${this.baseDomain}`);
        return true;
      }
      throw error;
    }
  }

  private handleCreateRecordError(error: any, serverId: string): boolean {
    if (error.response?.status === 422 &&
        error.response?.data?.message?.includes('already exists')) {
      this.logger.log(`DNS record already exists: ${serverId}.${this.baseDomain}`);
      return true;
    }

    this.logger.error(`Failed to create DNS record: ${error.message}`);
    return false;
  }

  private async deleteMatchingRecords(records: any[], serverId: string): Promise<boolean> {
    let success = true;
    let deletedCount = 0;

    for (const record of records) {
      if (record.name === serverId) {
        try {
          const response = await lastValueFrom(
            this.httpService.delete(
              `https://api.digitalocean.com/v2/domains/${this.baseDomain}/records/${record.id}`,
              { headers: this.getApiHeaders() }
            )
          );

          if (response.status === 204) {
            deletedCount++;
          } else {
            this.logger.warn(`Unexpected response when deleting DNS record: ${response.status}`);
            success = false;
          }
        } catch (deleteError) {
          this.logger.error(`Failed to delete DNS record: ${deleteError.message}`);
          success = false;
        }
      }
    }

    if (success) {
      this.logger.log(`DNS record deleted successfully: ${serverId}.${this.baseDomain} (${deletedCount} records)`);
    } else if (deletedCount > 0) {
      this.logger.log(`Partially deleted DNS records: ${serverId}.${this.baseDomain} (${deletedCount}/${records.length} records)`);
      success = true; // Consider partial success as success
    }

    return success;
  }
}
