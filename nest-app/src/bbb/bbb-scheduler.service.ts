import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { lastValueFrom } from 'rxjs';
import { AxiosError } from 'axios';
import { DnsService } from '../dns';
import { LectureCacheService } from '../cache';
import { AnsibleService } from '../ansible';
import {
  Lecture,
  Event,
  EventType,
  TimeInterval,
  ServerSize,
  ServerConfig,
  ServerAllocation,
  ServerOperation,
  ServerStatus
} from '../interfaces/bbb-scheduler.interface';

@Injectable()
export class BbbSchedulerService {
  private readonly logger = new Logger(BbbSchedulerService.name);
  private readonly HASURA_GRAPHQL_ENDPOINT = process.env.HASURA_GRAPHQL_ENDPOINT;
  private readonly HASURA_GRAPHQL_ADMIN_SECRET = process.env.HASURA_GRAPHQL_ADMIN_SECRET;
  private readonly serverConfigs: Record<ServerSize, ServerConfig> = {
    [ServerSize.MEDIUM]: {
      size: ServerSize.MEDIUM,
      vCPUs: 8,
      ram: 16,
      capacity: 160,
      effectiveCapacity: 128, // 80% of 160
      slug: 's-8vcpu-16gb',
    },
    [ServerSize.LARGE]: {
      size: ServerSize.LARGE,
      vCPUs: 16,
      ram: 32,
      capacity: 320,
      effectiveCapacity: 256, // 80% of 320
      slug: 'c-16',
    },
    [ServerSize.EXTRA_LARGE]: {
      size: ServerSize.EXTRA_LARGE,
      vCPUs: 16,
      ram: 64,
      capacity: 640, // Fixed to match README (640 users)
      effectiveCapacity: 512, // 80% of 640
      slug: 'g-16vcpu-64gb',
    },
  };

  private activeServers: Map<string, ServerAllocation> = new Map();
  private scheduledOperations: ServerOperation[] = [];
  private lastRunStatus: {
    timestamp: Date;
    success: boolean;
    message: string;
    serverAllocations?: ServerAllocation[];
    scheduledOperations?: ServerOperation[];
  } | null = null;

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
    private readonly dnsService: DnsService,
    private readonly lectureCacheService: LectureCacheService,
    private readonly ansibleService: AnsibleService,
  ) {
    this.validateAnsibleSetup();
  }

  /**
   * Validate Ansible setup and log the result
   */
  private async validateAnsibleSetup(): Promise<void> {
    try {
      const isValid = await this.ansibleService.validateAnsibleSetup();
      if (isValid) {
        this.logger.log('✅ Ansible setup validation successful');
      } else {
        this.logger.warn('⚠️ Ansible setup validation failed');
      }
    } catch (error) {
      this.logger.error('❌ Failed to validate Ansible setup', error.stack);
    }
  }

  /**
   * Fetches today's meetings from the GraphQL API
   */
  private async fetchTodaysMeetings(): Promise<Lecture[]> {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const endOfDay = new Date();
    endOfDay.setHours(23, 59, 59, 999);

    const startDate = today.toISOString();
    const endDate = endOfDay.toISOString();

    const query = `
    query BBBMeetings($startDate: timestamptz!, $endDate: timestamptz!) {
      calendar_event(where: {
        start_date: {_gte: $startDate, _lte: $endDate},
        isVirtual: {_eq: true},
        _not: {config: {_contains: {virtual_meeting: {external: true}}}}
      }) {
        uid
        start_date
        end_date
        user_events_aggregate {
          aggregate {
            count
          }
        }
      }
    }
  `;

    this.logger.log(`Fetching meetings for date range: ${startDate} to ${endDate}`);

    try {
      const response = await fetch(this.HASURA_GRAPHQL_ENDPOINT, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-hasura-admin-secret': this.HASURA_GRAPHQL_ADMIN_SECRET,
        },
        body: JSON.stringify({
          query,
          variables: { startDate, endDate },
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! Status: ${response.status}`);
      }

      const json = await response.json();
      if ('data' in json && 'calendar_event' in json.data) {
        const events = json.data.calendar_event as Array<{
          uid: string;
          start_date: string;
          end_date: string;
          user_events_aggregate: {
            aggregate: {
              count?: number;
            };
          };
        }>;

        const lectures = events.map((data) => {
          const startTime = new Date(data.start_date);
          const endTime = new Date(data.end_date);

          return {
            id: data.uid,
            start_time: startTime.toISOString(),
            end_time: endTime.toISOString(),
            num_participants: data.user_events_aggregate.aggregate.count ?? 0,
          };
        });

        this.logger.log(`Successfully fetched ${lectures.length} meetings from GraphQL API`);
        return lectures;
      }

      throw new Error('Invalid response format from GraphQL API');
    } catch (error) {
      this.logger.error(`Failed to fetch meetings from GraphQL API: ${error.message}`, error.stack);
      throw error;
    }
  }

  @Cron('*/5 * * * *') // Run every 5 minutes
  async manageServers() {
    this.logger.log('Running server management job');

    try {
      const lectures = await this.fetchTodaysMeetings();
      this.logger.log(`Fetched ${lectures.length} lectures for today`);

      const events = this.generateEvents(lectures);
      const timeIntervals = this.calculateTimeIntervals(events);
      const serverAllocations = this.allocateServers(timeIntervals);

      this.logger.log(`Generated ${serverAllocations.length} server allocations`);

      await this.updateScheduledOperations(serverAllocations, lectures);
      await this.executeScheduledOperations();
      await this.updateServerStatuses();
      await this.cleanupExpiredCacheEntries(serverAllocations);

      this.logger.log('Server management job completed successfully');

      this.lastRunStatus = {
        timestamp: new Date(),
        success: true,
        message: 'Server management completed successfully',
        serverAllocations,
        scheduledOperations: this.scheduledOperations,
      };

      return {
        success: true,
        message: 'Server management completed successfully',
        serverAllocations,
        scheduledOperations: this.scheduledOperations,
      };
    } catch (error) {
      this.logger.error('Error in server management job', error.stack);

      this.lastRunStatus = {
        timestamp: new Date(),
        success: false,
        message: `Error: ${error.message}`,
      };

      throw error;
    }
  }

  // Keep the original method for manual triggering via API
  async scheduleDailyServers() {
    return this.manageServers();
  }

  /**
   * Get the current status of the scheduler
   */
  getStatus() {
    return {
      lastRun: this.lastRunStatus,
      activeServers: Array.from(this.activeServers.entries()).map(([id, allocation]) => ({
        id,
        ...allocation,
      })),
      scheduledOperations: this.scheduledOperations,
    };
  }

  /**
   * Generates start and end events from lectures
   */
  private generateEvents(lectures: Lecture[]): Event[] {
    const events: Event[] = [];

    lectures.forEach(lecture => {
      events.push({
        time: new Date(lecture.start_time),
        type: EventType.START,
        lectureId: lecture.id,
        participants: lecture.num_participants,
      });

      events.push({
        time: new Date(lecture.end_time),
        type: EventType.END,
        lectureId: lecture.id,
        participants: lecture.num_participants,
      });
    });

    // Sort events chronologically
    // If two events occur at the same time, END events should come before START events
    events.sort((a, b) => {
      if (a.time.getTime() === b.time.getTime()) {
        return a.type === EventType.END ? -1 : 1;
      }
      return a.time.getTime() - b.time.getTime();
    });

    return events;
  }

  /**
   * Calculates time intervals with concurrent participants
   * Also creates intervals for individual future lectures
   */
  private calculateTimeIntervals(events: Event[]): TimeInterval[] {
    const timeIntervals: TimeInterval[] = [];
    let currentParticipants = 0;
    let activeLectures: Set<string> = new Set();
    let lastEventTime: Date | null = null;

    events.forEach((event, index) => {
      // If this isn't the first event and time has changed, create an interval
      if (lastEventTime && lastEventTime.getTime() !== event.time.getTime() && activeLectures.size > 0) {
        timeIntervals.push({
          start: new Date(lastEventTime),
          end: new Date(event.time),
          concurrentParticipants: currentParticipants,
          lectures: Array.from(activeLectures),
        });
      }

      // Update active lectures and participant count
      if (event.type === EventType.START) {
        activeLectures.add(event.lectureId);
        currentParticipants += event.participants;
      } else {
        activeLectures.delete(event.lectureId);
        currentParticipants -= event.participants;
      }

      lastEventTime = event.time;

      // If this is the last event or the next event is at a different time, create an interval
      const nextEvent = events[index + 1];
      if (activeLectures.size > 0 && (!nextEvent || nextEvent.time.getTime() !== event.time.getTime())) {
        timeIntervals.push({
          start: new Date(event.time),
          end: nextEvent ? new Date(nextEvent.time) : new Date(event.time.getTime() + 3600000), // Default 1 hour if last event
          concurrentParticipants: currentParticipants,
          lectures: Array.from(activeLectures),
        });
      }
    });

    // Also create intervals for individual lectures (even if not overlapping with others)
    const lectureMap = new Map<string, { start: Date; end: Date; participants: number }>();
    events.forEach(event => {
      if (event.type === EventType.START) {
        lectureMap.set(event.lectureId, {
          start: event.time,
          end: event.time,
          participants: event.participants,
        });
      } else if (event.type === EventType.END) {
        const lecture = lectureMap.get(event.lectureId);
        if (lecture) {
          lecture.end = event.time;
        }
      }
    });

    // Add individual lecture intervals if they're not already covered
    lectureMap.forEach((lecture, lectureId) => {
      const existingInterval = timeIntervals.find(interval =>
        interval.lectures.includes(lectureId)
      );

      if (!existingInterval) {
        timeIntervals.push({
          start: lecture.start,
          end: lecture.end,
          concurrentParticipants: lecture.participants,
          lectures: [lectureId],
        });
      }
    });

    return timeIntervals;
  }

  /**
   * Allocates servers based on time intervals using a bin packing algorithm
   */
  private allocateServers(timeIntervals: TimeInterval[]): ServerAllocation[] {
    const serverAllocations: ServerAllocation[] = [];
    const lectureMap = new Map<string, { start: Date; end: Date; participants: number }>();

    // First, build a map of all lectures with their start/end times and participants
    timeIntervals.forEach(interval => {
      interval.lectures.forEach(lectureId => {
        const existingLecture = lectureMap.get(lectureId);
        if (!existingLecture) {
          lectureMap.set(lectureId, {
            start: interval.start,
            end: interval.end,
            participants: interval.concurrentParticipants / interval.lectures.length, // Approximate
          });
        } else {
          // Update end time if this interval extends the lecture
          if (interval.end > existingLecture.end) {
            existingLecture.end = interval.end;
          }
        }
      });
    });

    // Group time intervals by overlapping periods
    const overlappingGroups: TimeInterval[][] = [];
    let currentGroup: TimeInterval[] = [];
    let currentEndTime: Date | null = null;

    // Sort intervals by start time
    const sortedIntervals = [...timeIntervals].sort((a, b) => a.start.getTime() - b.start.getTime());

    sortedIntervals.forEach(interval => {
      if (!currentEndTime || interval.start >= currentEndTime) {
        // Start a new group
        if (currentGroup.length > 0) {
          overlappingGroups.push(currentGroup);
        }
        currentGroup = [interval];
        currentEndTime = interval.end;
      } else {
        // Add to current group
        currentGroup.push(interval);
        // Update end time if this interval extends further
        if (interval.end > currentEndTime) {
          currentEndTime = interval.end;
        }
      }
    });

    // Add the last group if it exists
    if (currentGroup.length > 0) {
      overlappingGroups.push(currentGroup);
    }

    // Process each group of overlapping intervals
    overlappingGroups.forEach(group => {
      // Find the max concurrent participants in this group
      const maxParticipants = Math.max(...group.map(i => i.concurrentParticipants));

      // Determine the start and end times for this allocation
      const startTime = new Date(Math.min(...group.map(i => i.start.getTime())));
      const endTime = new Date(Math.max(...group.map(i => i.end.getTime())));

      // Get all unique lectures in this group
      const uniqueLectures = new Set<string>();
      group.forEach(interval => {
        interval.lectures.forEach(lectureId => uniqueLectures.add(lectureId));
      });

      // Determine server size based on participant count
      let serverSize: ServerSize;
      if (maxParticipants <= this.serverConfigs[ServerSize.MEDIUM].effectiveCapacity) {
        serverSize = ServerSize.MEDIUM;
      } else if (maxParticipants <= this.serverConfigs[ServerSize.LARGE].effectiveCapacity) {
        serverSize = ServerSize.LARGE;
      } else {
        serverSize = ServerSize.EXTRA_LARGE;
      }

      // If we need multiple servers, calculate how many
      const serverCapacity = this.serverConfigs[serverSize].effectiveCapacity;
      const serversNeeded = Math.ceil(maxParticipants / serverCapacity);

      // Create server allocations
      for (let i = 0; i < serversNeeded; i++) {
        serverAllocations.push({
          size: serverSize,
          lectures: Array.from(uniqueLectures),
          totalParticipants: Math.min(maxParticipants - i * serverCapacity, serverCapacity),
          startTime,
          endTime,
        });
      }
    });

    return serverAllocations;
  }

  /**
   * Updates scheduled server operations based on current lecture data
   */
  private async updateScheduledOperations(allocations: ServerAllocation[], lectures: Lecture[]): Promise<void> {
    // Create a map for quick lecture lookup by ID
    const lectureMap = new Map<string, Lecture>();
    lectures.forEach(lecture => {
      lectureMap.set(lecture.id, lecture);
    });

    // Clear any existing scheduled operations for future lectures
    // Keep operations that are already due or currently executing
    const now = new Date();
    this.scheduledOperations = this.scheduledOperations.filter(
      op => op.scheduledTime <= now
    );

    // Schedule spin-up operations (30 minutes before needed)
    for (const allocation of allocations) {
      // Check if we already have an active server for these lectures
      const lectureIds = allocation.lectures;
      const existingServer = this.findExistingServerForLectures(lectureIds, allocation.size);

      if (existingServer) {
        this.logger.log(`Server already exists for lectures: ${lectureIds.join(', ')}. Skipping spin-up operation.`);
        // Assign the existing server ID to this allocation for proper spin-down scheduling
        allocation.serverId = existingServer;

        // Update the existing server allocation with the current lectures if needed
        const existingAllocation = this.activeServers.get(existingServer);
        if (existingAllocation) {
          // Add any lectures that aren't already in the server's lecture list
          lectureIds.forEach(id => {
            if (!existingAllocation.lectures.includes(id)) {
              existingAllocation.lectures.push(id);
            }
          });
        }
        continue;
      }

      // Check cache to see if lectures are already processed
      const processedLectures = await Promise.all(
        lectureIds.map(id => this.lectureCacheService.isLectureProcessed(id))
      );

      const hasProcessedLecture = processedLectures.some(processed => processed);
      if (hasProcessedLecture) {
        this.logger.log(`One or more lectures already processed: ${lectureIds.join(', ')}. Skipping server creation to prevent duplicates.`);
        continue;
      }

      const spinUpTime = new Date(allocation.startTime.getTime() - 30 * 60 * 1000);

      // Create server immediately if we're within 30 minutes of lecture start, or schedule for future
      if (now >= spinUpTime && now < allocation.startTime) {
        // We're within the 30-minute window - create server immediately
        this.logger.log(`Creating server immediately for ${allocation.size} server (${Math.round((allocation.startTime.getTime() - now.getTime()) / 60000)} min away)`);

        // Mark lectures as scheduled in cache before creating server
        await Promise.all(
          allocation.lectures.map(lectureId => {
            const lecture = lectureMap.get(lectureId);
            if (lecture) {
              return this.lectureCacheService.markLectureAsScheduled(
                lecture.id,
                lecture.start_time,
                lecture.end_time,
                lecture.num_participants
              );
            }
          }).filter(Boolean)
        );

        this.scheduledOperations.push({
          operation: 'spin-up',
          serverSize: allocation.size,
          scheduledTime: new Date(now.getTime() - 1000),
          lectures: allocation.lectures,
        });
      } else if (spinUpTime > now) {
        // Future lecture - schedule normally
        this.logger.log(`Scheduling spin-up for ${allocation.size} server at ${spinUpTime.toISOString()}`);

        // Mark lectures as scheduled in cache when scheduling for future
        await Promise.all(
          allocation.lectures.map(lectureId => {
            const lecture = lectureMap.get(lectureId);
            if (lecture) {
              return this.lectureCacheService.markLectureAsScheduled(
                lecture.id,
                lecture.start_time,
                lecture.end_time,
                lecture.num_participants
              );
            }
          }).filter(Boolean)
        );

        this.scheduledOperations.push({
          operation: 'spin-up',
          serverSize: allocation.size,
          scheduledTime: spinUpTime,
          lectures: allocation.lectures,
        });
      } else {
        // Lecture has already started or ended
        this.logger.warn(`Lecture ${allocation.lectures.join(', ')} has already started or ended`);
      }
    }

    // Schedule spin-down operations (5 minutes after no longer needed)
    allocations.forEach(allocation => {
      // Only schedule spin-down if we have an existing server ID or if this allocation will get a server
      const existingServer = this.findExistingServerForLectures(allocation.lectures, allocation.size);
      const spinDownTime = new Date(allocation.endTime.getTime() + 5 * 60 * 1000);

      if (spinDownTime > now && (allocation.serverId || existingServer)) {
        const serverIdToUse = allocation.serverId || existingServer;
        this.logger.log(`Scheduling spin-down for ${allocation.size} server ${serverIdToUse} at ${spinDownTime.toISOString()}`);

        this.scheduledOperations.push({
          operation: 'spin-down',
          serverSize: allocation.size,
          scheduledTime: spinDownTime,
          serverId: serverIdToUse,
        });
      } else if (spinDownTime > now && !allocation.serverId && !existingServer) {
        this.logger.log(`Skipping spin-down scheduling for ${allocation.size} server - no server ID available yet`);
      }
    });

    // Sort operations by scheduled time
    this.scheduledOperations.sort((a, b) => a.scheduledTime.getTime() - b.scheduledTime.getTime());

    this.logger.log(`Updated scheduled operations: ${this.scheduledOperations.length} total operations`);
  }

  /**
   * Executes scheduled operations that are due
   */
  private async executeScheduledOperations(): Promise<void> {
    const now = new Date();
    const dueOperations = this.scheduledOperations.filter(op => op.scheduledTime <= now);

    if (dueOperations.length === 0) {
      return;
    }

    this.logger.log(`Executing ${dueOperations.length} due operations`);

    // Remove due operations from the scheduled list
    this.scheduledOperations = this.scheduledOperations.filter(op => op.scheduledTime > now);

    // Execute each due operation
    for (const operation of dueOperations) {
      try {
        if (operation.operation === 'spin-up') {
          const lectures = operation.lectures || [];
          let totalParticipants = 0;
          let startTime: Date | undefined;
          let endTime: Date | undefined;

          if (lectures.length > 0) {
            const lectureDetails = await Promise.all(
              lectures.map(lectureId => this.lectureCacheService.getLectureInfo(lectureId))
            );

            const validLectures = lectureDetails.filter(Boolean);
            totalParticipants = validLectures.reduce((sum, lecture) => sum + (lecture?.participants || 0), 0);

            if (validLectures.length > 0) {
              const startTimes = validLectures.map(l => new Date(l!.startTime));
              const endTimes = validLectures.map(l => new Date(l!.endTime));
              startTime = new Date(Math.min(...startTimes.map(d => d.getTime())));
              endTime = new Date(Math.max(...endTimes.map(d => d.getTime())));
            }
          }

          const serverId = await this.spinUpServer(
            operation.serverSize,
            lectures,
            totalParticipants,
            startTime,
            endTime
          );

          // Update server ID in any matching spin-down operations
          this.scheduledOperations
            .filter(op => op.operation === 'spin-down' && op.serverSize === operation.serverSize && !op.serverId)
            .forEach(op => {
              op.serverId = serverId;
            });
        } else if (operation.operation === 'spin-down') {
          if (operation.serverId) {
            this.logger.log(`Spinning down server ${operation.serverId} (${operation.serverSize})`);
            await this.spinDown(operation.serverId);
          } else {
            this.logger.warn(`Cannot spin down server: missing server ID for ${operation.serverSize} server scheduled at ${operation.scheduledTime.toISOString()}`);
          }
        }
      } catch (error) {
        this.logger.error(`Failed to execute server operation: ${error.message}`, error.stack);

        // Retry failed spin-up operations after a delay
        if (operation.operation === 'spin-up') {
          this.logger.log(`Scheduling retry for failed spin-up operation in 5 minutes`);
          const retryTime = new Date(now.getTime() + 5 * 60 * 1000);
          this.scheduledOperations.push({
            ...operation,
            scheduledTime: retryTime
          });
        }
      }
    }
  }

  /**
   * Spins up a new server using DigitalOcean API
   */
  private async spinUpServer(
    serverSize: ServerSize,
    lectures: string[] = [],
    totalParticipants: number = 0,
    startTime?: Date,
    endTime?: Date
  ): Promise<string> {
    try {
      const serverName = `bbb-${serverSize}-${new Date().getTime()}`;
      this.logger.log(`Spinning up ${serverSize} server: ${serverName}`);

      // Cache server as creating
      await this.cacheServerAsCreating(serverName, serverSize, lectures, totalParticipants, startTime, endTime);

      // Create DigitalOcean droplet
      const dropletId = await this.createDigitalOceanDroplet(serverSize, serverName);

      // Update cache with actual dropletId
      await this.updateCacheWithDropletId(serverName, dropletId, serverSize, lectures, totalParticipants, startTime, endTime);

      // Get server IP address
      const ipAddress = await this.getServerIpAddress(dropletId);

      // Setup DNS
      const dnsName = await this.setupServerDns(serverName, ipAddress);

      // Provision server
      const provisioningSuccessful = await this.provisionServerSynchronously(
        dropletId,
        serverSize,
        ipAddress,
        dnsName,
        serverName
      );

      if (!provisioningSuccessful) {
        this.logger.warn(`Server ${serverName} created but provisioning failed`);
      }

      // Store server information in active servers
      this.storeActiveServerInfo(dropletId, serverSize, lectures, totalParticipants, startTime, endTime, ipAddress, serverName);

      return dropletId;
    } catch (error) {
      if (error.isAxiosError) {
        this.logger.error(`DigitalOcean API error: ${error.message}`, error.response?.data);
        throw new Error(`Failed to spin up server: ${error.message}`);
      }
      throw error;
    }
  }

  /**
   * Caches server as creating state
   */
  private async cacheServerAsCreating(
    serverName: string,
    serverSize: ServerSize,
    lectures: string[],
    totalParticipants: number,
    startTime?: Date,
    endTime?: Date
  ): Promise<void> {
    await this.lectureCacheService.cacheServerAsCreating(
      serverName,
      serverName,
      serverSize,
      lectures,
      totalParticipants,
      startTime?.toISOString() || new Date().toISOString(),
      endTime?.toISOString() || new Date(Date.now() + 3600000).toISOString()
    );
  }

  /**
   * Creates DigitalOcean droplet
   */
  private async createDigitalOceanDroplet(serverSize: ServerSize, serverName: string): Promise<string> {
    const config = this.serverConfigs[serverSize];
    const apiToken = this.configService.get<string>('DIGITALOCEAN_API_TOKEN');
    const region = this.configService.get<string>('DIGITALOCEAN_DEFAULT_REGION');
    const image = this.configService.get<string>('DIGITALOCEAN_DEFAULT_IMAGE');
    const sshKeyIds = this.configService.get<string>('DIGITALOCEAN_SSH_KEY_IDS');

    if (!apiToken) {
      throw new Error('DIGITALOCEAN_API_TOKEN environment variable is not set');
    }

    const response = await lastValueFrom(
      this.httpService.post(
        'https://api.digitalocean.com/v2/droplets',
        {
          name: serverName,
          region: region || 'fra1',
          size: config.slug,
          image: image || 'ubuntu-22-04-x64',
          ssh_keys: sshKeyIds ? sshKeyIds.split(',').map(id => parseInt(id.trim())) : [],
          backups: false,
          ipv6: false,
          tags: ['bbb', 'ansible'],
          user_data: this.generateUserData(serverSize),
        },
        {
          headers: {
            Authorization: `Bearer ${apiToken}`,
            'Content-Type': 'application/json',
          },
        }
      )
    );

    const dropletId = response.data.droplet.id.toString();
    this.logger.log(`Server created successfully with ID: ${dropletId}`);

    // Wait for server to be ready
    await this.waitForServerReady(dropletId);

    return dropletId;
  }

  /**
   * Updates cache with actual droplet ID
   */
  private async updateCacheWithDropletId(
    serverName: string,
    dropletId: string,
    serverSize: ServerSize,
    lectures: string[],
    totalParticipants: number,
    startTime?: Date,
    endTime?: Date
  ): Promise<void> {
    const tempServerInfo = await this.lectureCacheService.getServerInfo(serverName);
    if (tempServerInfo) {
      await this.lectureCacheService.removeServer(serverName);
      await this.lectureCacheService.cacheServerAsCreating(
        dropletId,
        serverName,
        serverSize,
        lectures,
        totalParticipants,
        startTime?.toISOString() || new Date().toISOString(),
        endTime?.toISOString() || new Date(Date.now() + 3600000).toISOString()
      );
    }
  }

  /**
   * Gets server IP address from DigitalOcean API
   */
  private async getServerIpAddress(dropletId: string): Promise<string> {
    const apiToken = this.configService.get<string>('DIGITALOCEAN_API_TOKEN');

    const serverDetails = await lastValueFrom(
      this.httpService.get(`https://api.digitalocean.com/v2/droplets/${dropletId}`, {
        headers: {
          Authorization: `Bearer ${apiToken}`,
        },
      })
    );

    const ipAddress = serverDetails.data.droplet.networks.v4.find(
      (network: any) => network.type === 'public'
    )?.ip_address;

    if (!ipAddress) {
      throw new Error('Failed to get server IP address');
    }

    return ipAddress;
  }

  /**
   * Prepares DNS name for the server (returns subdomain part only)
   * The actual DNS record creation happens during provisioning
   */
  private async setupServerDns(serverName: string, ipAddress: string): Promise<string> {
    // Return the subdomain part (without .geerd.net)
    // This will be used to create: serverName.geerd.net -> ipAddress
    const subdomain = serverName; // e.g., "bbb-medium-1749642300223"
    this.logger.log(`Prepared DNS subdomain: ${subdomain} (will create ${subdomain}.geerd.net -> ${ipAddress})`);
    return subdomain;
  }

  /**
   * Stores server information in active servers map
   */
  private storeActiveServerInfo(
    dropletId: string,
    serverSize: ServerSize,
    lectures: string[],
    totalParticipants: number,
    startTime?: Date,
    endTime?: Date,
    ipAddress?: string,
    serverName?: string
  ): void {
    this.activeServers.set(dropletId, {
      size: serverSize,
      lectures: lectures,
      totalParticipants: totalParticipants,
      startTime: startTime || new Date(),
      endTime: endTime || new Date(Date.now() + 3600000),
      serverId: dropletId,
      ipAddress: ipAddress,
      serverName: serverName
    });
  }

  /**
   * Clean server spindown with proper cleanup order:
   * 1. Wait 5 minutes after lecture ends (handled by scheduler)
   * 2. Check for active meetings reliably
   * 3. Transfer recordings safely
   * 4. Cleanup DNS records (while server is still accessible)
   * 5. Remove from Scalelite load balancer
   * 6. Delete droplet
   * 7. Update cache
   */
  private async spinDown(serverId: string): Promise<void> {
    try {
      this.logger.log(`🔄 Starting spindown for server ${serverId}`);

      const serverInfo = this.activeServers.get(serverId);
      if (!serverInfo) {
        throw new Error(`Server ${serverId} not found in active servers`);
      }

      // Update server status to decommissioning
      await this.lectureCacheService.updateServerStatus(serverId, ServerStatus.DECOMMISSIONING);

      // Step 1-3: Run Ansible decommissioning (check meetings, transfer recordings)
      const decommissionResult = await this.ansibleService.decommissionServer(
        serverId,
        serverInfo.ipAddress,
        false, // Don't force decommission
        serverInfo.serverName // Pass the DNS name
      );

      if (!decommissionResult.success) {
        this.logger.warn(`⚠️ Ansible decommissioning failed for server ${serverId}, proceeding with manual cleanup`);
      }

      // Step 4: Cleanup DNS records FIRST (while server is still accessible)
      if (serverInfo.serverName) {
        try {
          this.logger.log(`🌐 Cleaning up DNS records for ${serverInfo.serverName}`);
          await this.cleanupDnsRecords(serverInfo.serverName);
        } catch (error) {
          this.logger.warn(`⚠️ DNS cleanup failed for ${serverInfo.serverName}: ${error.message}`);
          // Continue - DNS cleanup is not critical
        }
      }

      // Step 5: Remove from Scalelite load balancer (if decommission playbook failed)
      if (!decommissionResult.success) {
        try {
          this.logger.log(`⚖️ Manually removing server from Scalelite load balancer`);
          await this.cleanupScaleliteRegistration(serverId, serverInfo.ipAddress, serverInfo.serverName);
        } catch (error) {
          this.logger.warn(`⚠️ Scalelite cleanup failed for ${serverId}: ${error.message}`);
          // Continue - Scalelite cleanup is not critical for infrastructure
        }
      }

      // Step 6: Delete droplet (final infrastructure cleanup)
      try {
        this.logger.log(`☁️ Deleting DigitalOcean droplet ${serverId}`);
        await this.deleteDigitalOceanDroplet(serverId);
      } catch (error) {
        this.logger.error(`❌ Failed to delete droplet ${serverId}: ${error.message}`);
        // This is critical - if droplet can't be deleted, we have a problem
        throw error;
      }

      // Step 7: Update cache and active servers
      await this.lectureCacheService.removeServer(serverId);
      this.activeServers.delete(serverId);

      this.logger.log(`✅ Server ${serverId} spindown completed successfully`);
    } catch (error) {
      this.logger.error(`❌ Failed to spindown server ${serverId}: ${error.message}`, error.stack);

      // Always try to clean up cache even on failure
      try {
        await this.lectureCacheService.removeServer(serverId);
        this.activeServers.delete(serverId);
      } catch (cacheError) {
        this.logger.error(`Failed to clean up cache for ${serverId}: ${cacheError.message}`);
      }

      throw error;
    }
  }

  /**
   * Generates cloud-init user data for server provisioning
   */
  private generateUserData(serverSize: ServerSize): string {
    return `#!/bin/bash
echo "Setting up server (${serverSize})"
apt-get update
apt-get install -y python3 python3-pip --no-install-recommends
pip3 install ansible
mkdir -p /etc/ansible
touch /var/lib/cloud/instance/ansible-ready
echo "Server setup completed and ready for Ansible provisioning"`;
  }

  /**
   * Finds an existing server that can handle the given lectures
   * @param lectureIds Array of lecture IDs to check
   * @param requiredSize The required server size
   * @returns The server ID if found, undefined otherwise
   */
  private findExistingServerForLectures(lectureIds: string[], requiredSize: ServerSize): string | undefined {
    // Check active servers
    for (const [serverId, allocation] of this.activeServers.entries()) {
      // Check if server size matches or exceeds the required size
      if (this.serverConfigs[allocation.size].effectiveCapacity >= this.serverConfigs[requiredSize].effectiveCapacity) {
        // Check if this server already handles any of these lectures
        const hasCommonLectures = lectureIds.some(id => allocation.lectures.includes(id));
        if (hasCommonLectures) {
          this.logger.log(`Found existing server ${serverId} that already handles some of these lectures`);
          return serverId;
        }
      }
    }

    // Check scheduled operations
    const spinUpOps = this.scheduledOperations.filter(op => op.operation === 'spin-up');
    for (const op of spinUpOps) {
      if (op.lectures && this.serverConfigs[op.serverSize].effectiveCapacity >= this.serverConfigs[requiredSize].effectiveCapacity) {
        // Check if this operation is for any of these lectures
        const hasCommonLectures = lectureIds.some(id => op.lectures.includes(id));
        if (hasCommonLectures) {
          this.logger.log(`Found scheduled spin-up operation for some of these lectures`);
          return undefined; // We don't have a server ID yet, but we know one is scheduled
        }
      }
    }

    return undefined;
  }

  /**
   * Waits for server to be ready
   */
  private async waitForServerReady(dropletId: string): Promise<void> {
    try {
      const apiToken = this.configService.get<string>('DIGITALOCEAN_API_TOKEN');

      // Poll server status until it's active
      let isActive = false;
      let retries = 0;
      const maxRetries = 30; // 5 minutes (10 second intervals)

      while (!isActive && retries < maxRetries) {
        const response = await lastValueFrom(
          this.httpService.get(`https://api.digitalocean.com/v2/droplets/${dropletId}`, {
            headers: {
              Authorization: `Bearer ${apiToken}`,
            },
          })
        );

        isActive = response.data.droplet.status === 'active';

        if (!isActive) {
          await this.delay(10000); // Wait 10 seconds
          retries++;
        }
      }

      if (!isActive) {
        throw new Error(`Server did not become active after ${maxRetries * 10} seconds`);
      }

      this.logger.log(`Server ${dropletId} is now active`);
    } catch (error) {
      this.logger.error(`Error waiting for server to be ready: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Cleans up expired cache entries for lectures that have ended
   */
  private async cleanupExpiredCacheEntries(allocations: ServerAllocation[]): Promise<void> {
    try {
      const now = new Date();
      const endedLectureIds = new Set<string>();

      allocations.forEach(allocation => {
        if (allocation.endTime < now) {
          allocation.lectures.forEach(id => endedLectureIds.add(id));
        }
      });

      if (endedLectureIds.size > 0) {
        this.logger.log(`Cleaning up cache entries for ${endedLectureIds.size} ended lectures`);
        await this.lectureCacheService.cleanupOldLectures(24);
      }
    } catch (error) {
      this.logger.error('Error cleaning up expired cache entries', error.stack);
    }
  }

  /**
   * Updates server statuses based on current time and lecture schedules
   */
  private async updateServerStatuses(): Promise<void> {
    try {
      const now = new Date();
      const servers = await this.lectureCacheService.getAllServers();

      for (const serverInfo of servers) {
        const lectureStartTime = new Date(serverInfo.startTime);
        const lectureEndTime = new Date(serverInfo.endTime);
        const isLectureActive = now >= lectureStartTime && now <= lectureEndTime;

        if (isLectureActive && serverInfo.status === ServerStatus.READY) {
          await this.lectureCacheService.updateServerStatus(serverInfo.serverId, ServerStatus.READY);
          this.logger.log(`Server ${serverInfo.serverId} confirmed ready - lecture in progress`);
        }
      }
    } catch (error) {
      this.logger.error(`Failed to update server statuses: ${error.message}`, error.stack);
    }
  }

  /**
   * Waits for SSH connectivity to ensure server is ready for Ansible
   */
  private async waitForSshConnectivity(ipAddress: string): Promise<void> {
    const maxRetries = 30; // 5 minutes (10 second intervals)
    let retries = 0;

    while (retries < maxRetries) {
      try {
        const { spawn } = require('child_process');

        const testConnection = await new Promise<boolean>((resolve) => {
          const timeout = setTimeout(() => resolve(false), 5000);

          const nc = spawn('nc', ['-z', '-v', '-w', '3', ipAddress, '22'], {
            stdio: 'pipe'
          });

          nc.on('exit', (code) => {
            clearTimeout(timeout);
            resolve(code === 0);
          });

          nc.on('error', () => {
            clearTimeout(timeout);
            resolve(false);
          });
        });

        if (testConnection) {
          this.logger.log(`SSH port is open on ${ipAddress}, server is ready for Ansible`);
          return;
        }
      } catch (error) {
        this.logger.debug(`SSH connectivity check attempt ${retries + 1} failed: ${error.message}`);
      }

      retries++;
      if (retries < maxRetries) {
        await this.delay(10000);
      }
    }

    this.logger.warn(`SSH connectivity check timed out for ${ipAddress} after ${maxRetries} attempts, proceeding with Ansible anyway`);
  }

  /**
   * Non-blocking delay utility
   */
  private async delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Enhanced server provisioning with strict synchronous flow
   */
  private async provisionServerSynchronously(
    dropletId: string,
    serverSize: ServerSize,
    ipAddress: string,
    dnsName: string,
    serverName: string
  ): Promise<boolean> {
    this.logger.log(`Starting synchronous provisioning for server ${serverName}`);

    try {
      // DNS Creation and Verification
      let dnsCreated = false;
      if (dnsName) {
        this.logger.log(`🌐 Creating DNS record: ${dnsName} -> ${ipAddress}`);
        dnsCreated = await this.createDnsRecordWithVerification(dnsName, ipAddress);

        if (!dnsCreated) {
          this.logger.warn(`⚠️ DNS record creation failed for ${dnsName}, proceeding with IP-only setup`);
          // Continue without DNS - BBB can still be accessed via IP
        } else {
          this.logger.log(`✅ DNS record created successfully: ${dnsName}.geerd.net -> ${ipAddress}`);
        }
      }

      // Wait for Server SSH Readiness
      await this.waitForSshConnectivity(ipAddress);

      // Update server status to provisioning
      await this.lectureCacheService.updateServerStatus(dropletId, ServerStatus.PROVISIONING);

      // Run Ansible Provisioning
      const provisioningResult = await this.runAnsibleProvisioningWithRetry(
        dropletId, // Use droplet ID for inventory targeting
        serverSize,
        ipAddress,
        dnsCreated ? dnsName : undefined,
        dnsCreated ? dnsName : serverName // Pass server name for domain construction
      );

      if (provisioningResult) {
        await this.lectureCacheService.markServerAsReady(
          dropletId,
          ipAddress,
          dnsCreated ? dnsName : undefined
        );

        this.logger.log(`✅ Server ${serverName} (${ipAddress}) provisioned successfully`);
        return true;
      } else {
        this.logger.error(`❌ Server ${serverName} provisioning failed`);
        await this.lectureCacheService.updateServerStatus(dropletId, ServerStatus.CREATING);
        return false;
      }
    } catch (error) {
      this.logger.error(`❌ Error during synchronous provisioning of ${serverName}: ${error.message}`, error.stack);
      await this.lectureCacheService.updateServerStatus(dropletId, ServerStatus.CREATING);
      return false;
    }
  }

  /**
   * Creates DNS record with proper verification
   */
  private async createDnsRecordWithVerification(dnsName: string, ipAddress: string): Promise<boolean> {
    try {
      this.logger.log(`Creating DNS record: ${dnsName} -> ${ipAddress}`);

      // Create DNS record (no retry - DNS service handles this internally)
      const created = await this.dnsService.createDnsRecord(dnsName, ipAddress);
      if (!created) {
        this.logger.error(`Failed to create DNS record for ${dnsName}`);
        return false;
      }

      // Verify the DNS record was created correctly (single attempt)
      const verified = await this.dnsService.verifyDnsRecord(dnsName, ipAddress);
      if (!verified) {
        this.logger.warn(`DNS verification failed for ${dnsName}, but record was created`);
        // Don't fail if verification fails - the record might still be propagating
        return true;
      }

      this.logger.log(`DNS record created and verified: ${dnsName} -> ${ipAddress}`);
      return true;
    } catch (error) {
      this.logger.error(`Failed to create DNS record for ${dnsName}: ${error.message}`);
      return false;
    }
  }

  /**
   * Runs Ansible provisioning with retry logic
   */
  private async runAnsibleProvisioningWithRetry(
    dropletId: string, // Use droplet ID for inventory targeting
    serverSize: ServerSize,
    ipAddress: string,
    fqdn: string,
    serverName: string // Pass server name for domain construction
  ): Promise<boolean> {
    try {
      await this.retryOperation(
        async () => {
          const result = await this.ansibleService.provisionServer(
            dropletId, // Use droplet ID for inventory targeting
            serverSize,
            ipAddress,
            fqdn,
            serverName // Pass server name for domain construction
          );

          if (!result.success) {
            throw new Error(result.error || 'Ansible provisioning failed');
          }

          return result;
        },
        3,
        30000, // 30 second delay
        `Ansible provisioning for server ${serverName}`
      );

      this.logger.log(`Ansible provisioning completed successfully for server ${serverName}`);
      return true;
    } catch (error) {
      this.logger.error(`Ansible provisioning failed for server ${serverName}: ${error.message}`);
      return false;
    }
  }

  /**
   * Cleans up DNS records with retry logic
   */
  private async cleanupDnsRecords(serverName: string): Promise<void> {
    const dnsName = serverName;

    try {
      await this.retryOperation(
        async () => {
          const deleted = await this.dnsService.deleteDnsRecord(dnsName);
          if (!deleted) {
            throw new Error(`DNS deletion failed for ${dnsName}`);
          }
          return deleted;
        },
        3,
        2000,
        `DNS deletion for ${dnsName}`
      );

      this.logger.log(`DNS record deleted successfully for ${dnsName}`);
    } catch (error) {
      this.logger.warn(`Failed to delete DNS record for ${dnsName}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Deletes DigitalOcean droplet
   */
  private async deleteDigitalOceanDroplet(serverId: string): Promise<void> {
    const apiToken = this.configService.get<string>('DIGITALOCEAN_API_TOKEN');
    if (!apiToken) {
      throw new Error('DIGITALOCEAN_API_TOKEN environment variable is not set');
    }

    await lastValueFrom(
      this.httpService.delete(`https://api.digitalocean.com/v2/droplets/${serverId}`, {
        headers: {
          Authorization: `Bearer ${apiToken}`,
        },
      })
    );

    this.logger.log(`DigitalOcean droplet ${serverId} deleted successfully`);
  }

  /**
   * Manual Scalelite cleanup when Ansible decommission fails
   * This is a fallback method - the main cleanup should be handled by Ansible
   */
  private async cleanupScaleliteRegistration(serverId: string, ipAddress: string, serverName?: string): Promise<void> {
    try {
      this.logger.warn(`⚠️ Performing manual Scalelite cleanup as fallback for server ${serverId}`);
      this.logger.log(`ℹ️ Note: Scalelite cleanup should normally be handled by the Ansible decommission playbook`);

      // For now, just log that this would need to be implemented if needed
      // The Ansible playbook should handle this properly
      this.logger.log(`🔄 Attempting to find and remove server ${serverName || serverId} from Scalelite manually`);

      // Use the same SSH approach as the Ansible playbook
      const scaleliteConfig = {
        serverIp: '**************',
        serverUser: 'root',
        sshPassword: 'Np66W3PnuK4j'
      };

      const serverUrl = `https://${serverName || serverId}.geerd.net/bigbluebutton/api`;
      this.logger.log(`🔍 Looking for server ${serverId} in Scalelite with URL: ${serverUrl}`);

      // Get server list from Scalelite
      const listCommand = `sshpass -p "${scaleliteConfig.sshPassword}" ssh -o StrictHostKeyChecking=no -o ConnectTimeout=10 ${scaleliteConfig.serverUser}@${scaleliteConfig.serverIp} "docker exec scalelite-api bundle exec rake servers"`;

      const listResult = await this.executeCommand(listCommand);

      if (listResult.success) {
        this.logger.log(`📋 Scalelite servers list:\n${listResult.output}`);

        if (listResult.output.includes(serverUrl)) {
          this.logger.warn(`⚠️ Server ${serverId} still found in Scalelite after Ansible decommission failed`);
          this.logger.log(`🛠️ Manual Scalelite removal would need to be implemented here if required`);
          this.logger.log(`💡 Recommendation: Fix the Ansible decommission playbook instead of implementing manual cleanup`);
        } else {
          this.logger.log(`✅ Server ${serverId} not found in Scalelite (may have been removed by another process)`);
        }
      } else {
        this.logger.warn(`⚠️ Failed to get Scalelite server list: ${listResult.error}`);
      }
    } catch (error) {
      this.logger.error(`❌ Error during manual Scalelite cleanup attempt: ${error.message}`);
      // Don't throw - this is just a fallback attempt
    }
  }

  /**
   * Execute shell command and return result
   */
  private async executeCommand(command: string): Promise<{ success: boolean; output: string; error?: string }> {
    return new Promise((resolve) => {
      const { exec } = require('child_process');
      exec(command, { timeout: 30000 }, (error, stdout, stderr) => {
        if (error) {
          resolve({
            success: false,
            output: stdout || '',
            error: error.message || stderr
          });
        } else {
          resolve({
            success: true,
            output: stdout || ''
          });
        }
      });
    });
  }

  /**
   * Generic retry utility method
   */
  private async retryOperation<T>(
    operation: () => Promise<T>,
    maxRetries: number,
    delayMs: number = 2000,
    operationName: string = 'operation'
  ): Promise<T> {
    let lastError: Error;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error;
        if (attempt < maxRetries) {
          this.logger.warn(`${operationName} attempt ${attempt}/${maxRetries} failed: ${error.message}, retrying...`);
          await this.delay(delayMs);
        }
      }
    }

    throw new Error(`${operationName} failed after ${maxRetries} attempts: ${lastError.message}`);
  }

  /**
   * Standardized error handling utility for catching and logging errors
   */
  private handleError(error: any, context: string, shouldThrow: boolean = true): void {
    const errorMessage = error?.message || 'Unknown error';

    if (error?.isAxiosError) {
      this.logger.error(`${context} - DigitalOcean API error: ${errorMessage}`, error.response?.data);
    } else {
      this.logger.error(`${context}: ${errorMessage}`, error.stack);
    }

    if (shouldThrow) {
      throw error;
    }
  }
}
