[Unit]
Description=BBB Raw File Processor
After=network.target
Wants=network.target

[Service]
Type=simple
User=bigbluebutton
Group=bigbluebutton
ExecStart=/usr/bin/ruby /usr/local/bin/raw_processor.rb
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal
SyslogIdentifier=raw-processor

# Security settings
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/var/bigbluebutton /var/log/bigbluebutton /tmp

# Environment
Environment=HOME=/var/lib/bigbluebutton
WorkingDirectory=/var/lib/bigbluebutton

[Install]
WantedBy=multi-user.target
