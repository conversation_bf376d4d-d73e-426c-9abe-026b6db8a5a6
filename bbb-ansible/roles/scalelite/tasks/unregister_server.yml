---
# Tasks for unregistering a BBB server from Scalelite load balancer

# Skip if Scalelite is offline
- name: Skip unregistration if Scalelite offline
  debug:
    msg: "Scalelite server unreachable. Skipping unregistration."
  when: not scalelite_online

- name: End play if Scalelite offline
  meta: end_play
  when: not scalelite_online

# Check if server exists in Scalelite
- name: Check if server exists in Scalelite
  debug:
    msg: "Server {{ server.server_name }} {{ 'found' if scalelite_server_id != '' else 'not found' }} in Scalelite (ID: {{ scalelite_server_id | default('None') }})"

# Disable server in Scalelite first (with retries)
- name: Disable server in Scalelite
  command: >-
    sshpass -p "{{ scalelite.ssh_password | default('Np66W3PnuK4j') }}" ssh -o StrictHostKeyChecking=no
    {{ scalelite.server_user | default('root') }}@{{ scalelite.server_ip | default('**************') }}
    "docker exec scalelite-api bundle exec rake \"servers:disable[{{ scalelite_server_id }}]\""
  register: scalelite_disable
  failed_when: false
  changed_when: scalelite_disable.rc == 0
  retries: 3
  delay: 5
  when:
    - scalelite_server_id is defined
    - scalelite_server_id != ''

# Remove server from Scalelite (with retries)
- name: Remove server from Scalelite
  command: >-
    sshpass -p "{{ scalelite.ssh_password | default('Np66W3PnuK4j') }}" ssh -o StrictHostKeyChecking=no
    {{ scalelite.server_user | default('root') }}@{{ scalelite.server_ip | default('**************') }}
    "docker exec scalelite-api bundle exec rake \"servers:remove[{{ scalelite_server_id }}]\""
  register: scalelite_remove
  failed_when: scalelite_remove.rc != 0
  changed_when: scalelite_remove.rc == 0
  retries: 3
  delay: 5
  when:
    - scalelite_server_id is defined
    - scalelite_server_id != ''

# Display unregistration status
- name: Display unregistration status
  debug:
    msg: >
      {% if scalelite_server_id is defined and scalelite_server_id != '' and scalelite_remove.rc == 0 %}
      ✅ Server {{ server.server_name }} successfully removed from Scalelite.
      {% elif scalelite_server_id is defined and scalelite_server_id != '' %}
      ❌ Failed to remove server {{ server.server_name }} from Scalelite. Check configuration.
      {% else %}
      ℹ️  Server {{ server.server_name }} not found in Scalelite. May have already been removed.
      {% endif %}
