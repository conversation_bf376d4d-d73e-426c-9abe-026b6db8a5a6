#!/usr/bin/env ruby
# frozen_string_literal: true

# BBB Raw File Processor - Fixed Version
# Processes raw recording files transferred from BBB servers

require 'fileutils'
require 'logger'
require 'timeout'
require 'thread'
require 'set'

# Configuration constants
PROCESSING_DIR = '/var/bigbluebutton/processing'
RAW_DIR = '/var/bigbluebutton/recording/raw'
PUBLISHED_DIR = '/var/bigbluebutton/published'
MAX_CONCURRENT_PROCESSES = 2
PROCESS_TIMEOUT = 3600 # 1 hour timeout per recording
POLL_INTERVAL = 10 # seconds - increased for better stability

class RawProcessor
  def initialize
    @logger = setup_logger
    @processing_queue = Queue.new
    @active_processes = {}
    @process_mutex = Mutex.new
    @running = true
    @processed_files = Set.new

    setup_signal_handlers
    ensure_directories
    ensure_permissions
  end

  def start
    @logger.info("🎬 BBB Raw File Processor starting...")
    @logger.info("📁 Monitoring: #{PROCESSING_DIR}")
    @logger.info("📁 Raw directory: #{RAW_DIR}")
    @logger.info("⚙️ Max concurrent processes: #{MAX_CONCURRENT_PROCESSES}")

    # Process any existing files
    process_existing_files

    # Start worker threads
    workers = []
    MAX_CONCURRENT_PROCESSES.times do |i|
      workers << Thread.new { worker_thread(i) }
    end

    # Start file monitor thread
    monitor_thread = Thread.new { file_monitor }

    @logger.info("✅ Processor fully initialized and running!")

    # Wait for all threads
    [monitor_thread, *workers].each(&:join)
  rescue => e
    @logger.error("❌ Fatal error: #{e.message}")
    @logger.error("Backtrace: #{e.backtrace.join("\n")}")
    exit(1)
  end

  private

  def setup_logger
    log_dir = '/var/log/bigbluebutton'
    FileUtils.mkdir_p(log_dir) unless Dir.exist?(log_dir)

    logger = Logger.new("#{log_dir}/raw_processor.log", 'weekly')
    logger.level = Logger::INFO
    logger.formatter = proc do |severity, datetime, progname, msg|
      "[#{datetime.strftime('%Y-%m-%d %H:%M:%S')}] #{severity}: #{msg}\n"
    end
    logger
  end

  def setup_signal_handlers
    %w[INT TERM].each do |signal|
      Signal.trap(signal) do
        @logger.info("🛑 Received #{signal} signal, shutting down gracefully...")
        @running = false
      end
    end
  end

  def ensure_directories
    [PROCESSING_DIR, RAW_DIR].each do |dir|
      FileUtils.mkdir_p(dir) unless Dir.exist?(dir)
    end
  end

  def ensure_permissions
    # Ensure proper ownership
    system("chown -R bigbluebutton:bigbluebutton #{PROCESSING_DIR} #{RAW_DIR}")
  end

  def process_existing_files
    existing_files = Dir.glob("#{PROCESSING_DIR}/*_raw.tar.gz").sort_by { |f| File.mtime(f) }

    if existing_files.any?
      @logger.info("📥 Found #{existing_files.length} existing files to process")
      existing_files.each do |file|
        @processing_queue.push(file)
        @processed_files.add(file)
      end
    else
      @logger.info("📭 No existing files found")
    end
  end

  def file_monitor
    @logger.info("👁️ File monitor thread started")

    while @running
      begin
        current_files = Dir.glob("#{PROCESSING_DIR}/*_raw.tar.gz")
        new_files = current_files - @processed_files.to_a

        new_files.each do |file|
          next unless File.exist?(file) && File.size(file) > 0

          # Wait for file to be completely written
          if file_stable?(file)
            @logger.info("📨 New file detected: #{File.basename(file)}")
            @processing_queue.push(file)
            @processed_files.add(file)
          end
        end

        # Clean up tracked files that no longer exist
        @processed_files.select! { |f| File.exist?(f) }

        sleep POLL_INTERVAL
      rescue => e
        @logger.error("❌ Error in file monitor: #{e.message}")
        sleep 30
      end
    end

    @logger.info("👁️ File monitor thread stopped")
  end

  def file_stable?(file, wait_time = 10)
    return false unless File.exist?(file)

    initial_size = File.size(file)
    initial_mtime = File.mtime(file)

    sleep(wait_time)

    return false unless File.exist?(file)

    File.size(file) == initial_size && File.mtime(file) == initial_mtime
  end

  def worker_thread(worker_id)
    @logger.info("👷 Worker thread #{worker_id} started")

    while @running
      begin
        # Use timeout to prevent blocking indefinitely
        tar_file = Timeout.timeout(30) { @processing_queue.pop }

        if File.exist?(tar_file)
          process_raw_file(tar_file, worker_id)
        else
          @logger.warn("⚠️ File no longer exists: #{tar_file}")
        end
      rescue Timeout::Error
        # Queue was empty, continue
        next
      rescue => e
        @logger.error("❌ Error in worker #{worker_id}: #{e.message}")
        sleep 10
      end
    end

    @logger.info("👷 Worker thread #{worker_id} stopped")
  end

  def process_raw_file(tar_file, worker_id)
    # Extract meeting ID from filename - handle both formats
    filename = File.basename(tar_file, '_raw.tar.gz')
    meeting_id = filename.split('-').first # Take the part before the first dash

    @process_mutex.synchronize do
      return if @active_processes[meeting_id]
      @active_processes[meeting_id] = { worker: worker_id, start_time: Time.now }
    end

    @logger.info("🚀 Worker #{worker_id} processing: #{meeting_id} from #{File.basename(tar_file)}")
    start_time = Time.now

    begin
      # Validate tar file
      unless validate_tar_file(tar_file)
        @logger.error("❌ Invalid tar file: #{tar_file}")
        return false
      end

      # Extract with timeout
      unless extract_tar_file(tar_file, meeting_id)
        @logger.error("❌ Failed to extract: #{tar_file}")
        return false
      end

      # Trigger BBB processing with timeout
      unless trigger_bbb_processing(meeting_id)
        @logger.error("❌ Failed to trigger BBB processing: #{meeting_id}")
        return false
      end

      # Clean up tar file after successful processing
      FileUtils.rm_f(tar_file)
      @processed_files.delete(tar_file)
      
      @logger.info("✅ Successfully processed #{meeting_id} in #{(Time.now - start_time).round(2)}s")

      true
    rescue => e
      @logger.error("❌ Error processing #{meeting_id}: #{e.message}")
      @logger.error("Backtrace: #{e.backtrace.join("\n")}")
      false
    ensure
      @process_mutex.synchronize do
        @active_processes.delete(meeting_id)
      end
    end
  end

  def validate_tar_file(tar_file)
    return false unless File.exist?(tar_file) && File.size(tar_file) > 0

    # Test tar file integrity
    system("tar -tzf '#{tar_file}' > /dev/null 2>&1")
  end

  def extract_tar_file(tar_file, meeting_id)
    @logger.info("📦 Extracting #{File.basename(tar_file)} to #{RAW_DIR}")
    
    extraction_cmd = "cd '#{RAW_DIR}' && tar -xzf '#{tar_file}' 2>/dev/null"

    Timeout.timeout(300) do # 5 minute timeout for extraction
      success = system(extraction_cmd)
      if success
        # Check if extraction created the expected directory
        possible_dirs = Dir.glob("#{RAW_DIR}/#{meeting_id}*")
        if possible_dirs.any?
          actual_meeting_dir = possible_dirs.first
          @logger.info("📁 Extracted to: #{actual_meeting_dir}")
          
          # Ensure proper ownership
          system("chown -R bigbluebutton:bigbluebutton '#{actual_meeting_dir}'")
          return true
        else
          @logger.error("❌ No meeting directory found after extraction")
          return false
        end
      end
      false
    end
  rescue Timeout::Error
    @logger.error("❌ Extraction timeout for #{meeting_id}")
    false
  end

  def trigger_bbb_processing(meeting_id)
    @logger.info("🎬 Triggering BBB processing for #{meeting_id}")
    
    # Find the actual meeting directory (might have timestamp suffix)
    possible_dirs = Dir.glob("#{RAW_DIR}/#{meeting_id}*")
    if possible_dirs.empty?
      @logger.error("❌ No meeting directory found for #{meeting_id}")
      return false
    end
    
    actual_meeting_id = File.basename(possible_dirs.first)
    meeting_dir = possible_dirs.first
    
    @logger.info("📁 Using meeting directory: #{meeting_dir}")

    # Ensure proper ownership and permissions
    system("chown -R bigbluebutton:bigbluebutton '#{meeting_dir}'")
    system("chmod -R 755 '#{meeting_dir}'")

    # Create done file to signal processing readiness
    done_file = File.join(meeting_dir, "#{actual_meeting_id}.done")
    File.write(done_file, Time.now.to_s)
    system("chown bigbluebutton:bigbluebutton '#{done_file}'")

    # Trigger BBB processing with timeout
    process_cmd = "bbb-record --rebuild '#{actual_meeting_id}'"
    @logger.info("🔄 Running: #{process_cmd}")

    Timeout.timeout(120) do # 2 minute timeout for command execution
      success = system(process_cmd)
      if success
        @logger.info("✅ BBB processing triggered successfully for #{actual_meeting_id}")
      else
        @logger.error("❌ BBB processing command failed for #{actual_meeting_id}")
      end
      success
    end
  rescue Timeout::Error
    @logger.error("❌ BBB processing trigger timeout for #{meeting_id}")
    false
  end
end

# Run the processor
if __FILE__ == $0
  processor = RawProcessor.new
  processor.start
end
